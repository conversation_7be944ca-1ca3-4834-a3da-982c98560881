import * as React from 'react';
import Svg, {ClipPath, Defs, G, Image, Path, SvgProps} from 'react-native-svg';
import {Dimensions, View} from 'react-native';
import ItemHandle from './ItemHandle.tsx';
import FastImage from 'react-native-fast-image';
import {heightScreen, widthScreen} from '../../../src/utils/Scale.ts';

const {width, height} = Dimensions.get('screen');
const dataItem = [
  {id: 'b1', x: 135, y: 346},
  {id: 'b2', x: 672, y: 555},
  {id: 'b3', x: 537, y: 933},
  {id: 'b4', x: 183, y: 1266},
  {id: 'b5', x: 740, y: 1445},
  {id: 'b6', x: 557, y: 1796},
  {id: 'b7', x: 151, y: 2095},
  {id: 'b8', x: 743, y: 2282},
];

interface ItemQuest2 extends SvgProps {
  data: Array<any>;
  onItemPress: (item: any, index: number) => void;
  active: number;
}

const ItemQuest2: React.FC<ItemQuest2> = ({data, onItemPress, active}) => (
  <View style={{width, height, marginBottom: -3}}>
    <FastImage
      source={require('./bgItem2.png')}
      style={{
        position: 'absolute',
        width: widthScreen,
        height: heightScreen,
      }}
      resizeMode="cover"
    />
    <Svg width={'100%'} height={'100%'} viewBox="0 0 1179 2556" fill="none">
      <G id="Loop Map 1 copy2" clipPath="url(#clip0_247_2545)">
        {dataItem.map((item, index) => (
          <ItemHandle
            key={item.id}
            onItemPress={() => {
              if (!data[index]) {
                return;
              }
              onItemPress(data[index], index);
            }}
            y={item.y}
            x={item.x}
            status={
              (data[index]?.statusAssign == 1 ||
                data[index]?.statusAssign == 2) &&
              data[index]?.numberOfAttempt > 0
                ? 2
                : (data[index]?.statusAssign === 1 &&
                      (data[index]?.attemptLimit == null ||
                        data[index]?.numberOfAttempt <
                          data[index]?.attemptLimit)) ||
                    (data[index]?.statusAssign === 2 &&
                      data[index]?.attemptLimit == null)
                  ? 1
                  : data[index]
                    ? 0
                    : 4
            }
          />
        ))}
      </G>
      <Defs>
        <ClipPath id="a">
          <Path fill="#fff" d="M0 0h1179v2556H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  </View>
);
export default ItemQuest2;
