import * as React from 'react';
import Svg, {ClipPath, Defs, G, Image, Path, SvgProps} from 'react-native-svg';
import {Dimensions, View} from 'react-native';
import ItemHandle from './ItemHandle.tsx';
import FastImage from 'react-native-fast-image';
import {heightScreen, widthScreen} from '../../../src/utils/Scale.ts';

const {width, height} = Dimensions.get('screen');
const dataItem = [
  {id: 'b1', x: 205, y: 418},
  {id: 'b2', x: 695, y: 574},
  {id: 'b3', x: 607, y: 958},
  {id: 'b4', x: 183, y: 1241},
  {id: 'b5', x: 606, y: 1409},
  {id: 'b6', x: 637, y: 1788},
  {id: 'b7', x: 156, y: 2060},
  {id: 'b8', x: 742, y: 2294},
];

interface ItemQuest1 extends SvgProps {
  data: Array<any>;
  onItemPress: (item: any, index: number) => void;
  active: number;
}

const ItemQuest1: React.FC<ItemQuest1> = ({data, onItemPress, active}) => (
  <View style={{width, height, marginBottom: -3}}>
    <FastImage
      source={require('./bgItem1.png')}
      style={{
        position: 'absolute',
        width: widthScreen,
        height: heightScreen,
      }}
      resizeMode="cover"
    />
    <Svg width={'100%'} height={'100%'} viewBox="0 0 1179 2556" fill="none">
      <G id="Loop Map 1 copy 1" clipPath="url(#clip0_247_2545)">
        {dataItem.map((item, index) => (
          <ItemHandle
            key={item.id}
            onItemPress={() => {
              if (!data[index]) {
                return;
              }
              onItemPress(data[index], index);
            }}
            y={item.y}
            x={item.x}
            status={
              (data[index]?.statusAssign == 1 ||
                data[index]?.statusAssign == 2) &&
              data[index]?.numberOfAttempt > 0
                ? 2
                : (data[index]?.statusAssign === 1 &&
                      (data[index]?.attemptLimit == null ||
                        data[index]?.numberOfAttempt <
                          data[index]?.attemptLimit)) ||
                    (data[index]?.statusAssign === 2 &&
                      data[index]?.attemptLimit == null)
                  ? 1
                  : data[index]
                    ? 0
                    : 4
            }
          />
        ))}
      </G>
    </Svg>
  </View>
);
export default ItemQuest1;
